import React, {forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState} from 'react';
import {Left, LeftVertical, Return, Right} from '@baidu/xicon-react-bigdata';
import {Button, Link, Modal, Pagination, Table, toast} from 'acud';
import {useRequest} from 'ahooks';
import {Clipboard} from '@baidu/bce-react-toolkit';

import * as http from '@api/metaRequest';
import {formatBytes} from '@utils/utils';
import IconSvg from '@components/IconSvg';
import {getIconTypeFromName} from '../helper';
import TextEllipsis from '@components/TextEllipsisTooltip';
import {ColumnsType} from 'acud/lib/table';

function reducePathLevel(path) {
  // 如果路径以 '/' 结尾，先去掉末尾的 '/'
  if (path.endsWith('/')) {
    path = path.slice(0, -1);
  }
  // 找到最后一个 '/' 的位置
  const lastSlashIndex = path.lastIndexOf('/');
  // 如果存在 '/'，则截取到最后一个 '/' 之前的部分
  if (lastSlashIndex !== -1) {
    return path.slice(0, lastSlashIndex + 1);
  }
  return;
}

// 定义通过 ref 暴露的方法类型
export interface VolumeFileRefHandle {
  requestFilelist: () => void;
}

// 自定义分页器枚举类型
enum PaginationType {
  MOVE_TO_FIRST = 'MOVE_TO_FIRST',
  LEFT_SHIFT = 'LEFT_SHIFT',
  RIGHT_SHIFT = 'RIGHT_SHIFT'
}

const klass = 'volume-file-table';

const VolumeFileInfoTable = (props: any, ref: React.Ref<VolumeFileRefHandle>) => {
  const {urlState, changeUrlFun} = props;
  const {catalog = '', schema = '', node = '', path = ''} = urlState;

  // volume 全名
  const fullName = `${catalog}.${schema}.${node}`;

  const pathArr = useMemo(() => (path ? path.split('/') : []), [path]);

  // 虚拟路径
  const volumePath = `/Volumes/${catalog}/${schema}/${node}/`;

  const [pagination, setPagination] = useState<{marker: string; preMarker: string[]; maxSize: number}>({
    marker: '',
    preMarker: [],
    maxSize: 10
  });

  const [fileListData, setFileListData] = useState<http.IVolumeListRes>({} as any);

  const [selectedRowKeys, setSrKeys] = useState<any>([]);
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
      console.log('selectedRowKeys:', selectedRowKeys, 'selectedRows: ', selectedRows);
      setSrKeys(selectedRowKeys);
    }
  };

  useImperativeHandle(ref, () => ({
    // 父组件可调用该方法，重刷列表
    requestFilelist: () => {
      setPagination((pre) => ({...pre, marker: '', preMarker: []}));
      getVolumeFileList();
    }
  }));

  // 请求 Volume 文件列表
  const {loading, run: getVolumeFileList} = useRequest(
    async () => {
      if (!volumePath) {
        return;
      }
      const res = await http.getVolumeFileList(fullName, {
        path: volumePath + path,
        marker: pagination.marker,
        maxSize: pagination.maxSize
      });
      const result: any = res.result || {};
      result.files = result?.files?.map((item) => ({...item, key: item.path})) || [];
      setFileListData(result);
    },
    {
      manual: true,
      refreshDeps: [catalog, schema, node, pagination, volumePath]
    }
  );

  // 初始化
  useEffect(() => {
    if (!(catalog && schema && node)) {
      return;
    }
    getVolumeFileList();
  }, [catalog, schema, node, path, getVolumeFileList]);

  const basePath = `/Volumes/${catalog}/${schema}/${node}/`;

  // 文件路径跳转处理
  const goNextPath = (path: string) => {
    changeUrlFun((pre) => ({...pre, path: pre.path ? `${pre.path}${path}` : path}));
  };
  const goBackPath = () => {
    changeUrlFun((pre) => {
      return {...pre, path: reducePathLevel(pre.path)};
    });
  };
  const goToPath = (index: number) => {
    console.log(index, pathArr);
    const newPathArr = pathArr.slice(0, index + 1);
    const path = newPathArr.join('/');
    changeUrlFun((pre) => ({...pre, path: path ? path + '/' : undefined}));
  };

  // 文件删除方法
  const deleteVolumeFile = async (fileInfo?: any) => {
    const res = await http.deleteVolumeFile(fullName, {
      files: fileInfo ? [fileInfo.path] : selectedRowKeys
    });
    if (res.success) {
      // 重新刷新当前列表 跳转首页
      changePagination(PaginationType.MOVE_TO_FIRST);
      // 如果是批量 清空选择
      !fileInfo && setSrKeys([]);
    }
  };
  const onFileDelete = (fileInfo?: any) => {
    Modal.confirm({
      title: '确定要删除吗？',
      content: fileInfo ? (
        <div>删除后⽆法恢复！请确定是否要删除 “{fileInfo?.name}”</div>
      ) : (
        <div>删除后⽆法恢复！请确定是否要删除所选择的内容</div>
      ),
      onOk: () => (fileInfo ? deleteVolumeFile(fileInfo) : deleteVolumeFile())
    });
  };

  // 文件下载
  const onFileDownload = async (fileInfo: any) => {
    await http.downloadVolumeFile(fullName, {file: fileInfo.path});
  };

  // 分页方法
  const changePagination = (type: `${PaginationType}` | null, size?: number) => {
    const preMarker = pagination.preMarker;
    switch (type) {
      case PaginationType.MOVE_TO_FIRST: // 移动到最左侧
        setPagination((pre) => ({...pre, marker: '', preMarker: []}));
        break;
      case PaginationType.LEFT_SHIFT: {
        const marker = preMarker.pop();
        setPagination((pre) => ({...pre, marker: preMarker.length ? marker || '' : '', preMarker}));
        break;
      }
      case PaginationType.RIGHT_SHIFT: {
        preMarker.push(pagination.marker);
        setPagination((pre) => ({...pre, marker: fileListData.nextMarker, preMarker: preMarker}));
        break;
      }
      default:
        setPagination((pre) => ({...pre, maxSize: size ?? 30, marker: '', preMarker: []}));
    }
  };

  useEffect(() => {
    getVolumeFileList();
  }, [pagination]);

  const columns: ColumnsType = [
    {
      title: '⽂件名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      width: 300,
      render: (text: string) => {
        const isFolder = ~text.indexOf('/');
        const [iconType, color] = getIconTypeFromName(text);
        return isFolder ? (
          <span className="table-file-name">
            <IconSvg className="table-file-name-icon" type="metadata-folder" size={16} color="#FF9F0B" />
            <TextEllipsis tooltip={text}>
              <Link onClick={() => goNextPath(text)}>{text}</Link>
            </TextEllipsis>
          </span>
        ) : (
          <span className="table-file-name">
            <IconSvg className="table-file-name-icon" type={iconType} size={16} color={color} />
            <TextEllipsis tooltip={text}>{text}</TextEllipsis>
          </span>
        );
      }
    },
    {
      title: '文件大小',
      dataIndex: 'size',
      key: 'size',
      width: 100,
      render: (value: string) => {
        return value ? formatBytes(value) : '-';
      }
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 160,
      render: (value: string) => value || '-'
    },
    {
      title: '操作',
      dataIndex: '',
      width: 200,
      fixed: 'right',
      key: 'x',
      render: (_: any, record) => {
        return (
          <div className={'file-table-action'}>
            <Clipboard
              text={`${basePath}${path}${_.name}`}
              successMessage="复制成功"
              className="clipboard-inline-btn"
            >
              <Button type="actiontext">复制路径</Button>
            </Clipboard>
            <Button type="actiontext" onClick={() => onFileDelete(_)}>
              删除
            </Button>
            {~_.name.indexOf('/') ? null : (
              <Button type="actiontext" onClick={() => onFileDownload(_)}>
                下载
              </Button>
            )}
          </div>
        );
      }
    }
  ];

  return (
    <div className={`${klass}`}>
      <div className={`${klass}-head`}>
        <div className={`${klass}-action`}>
          <Button disabled={!selectedRowKeys.length} onClick={() => onFileDelete()}>
            批量删除
          </Button>
          <>
            {path ? (
              <>
                {' '}
                <Button type="actiontext" onClick={() => goBackPath()}>
                  <Return
                    theme="line"
                    size={16}
                    strokeWidth={1}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  返回上级
                </Button>{' '}
                <span className="splitting-symbol">|</span>
              </>
            ) : null}

            <Link
              className={`${klass}-default-path`}
              type={path ? 'default' : 'text'}
              onClick={() => {
                changeUrlFun((pre) => ({...pre, path: undefined}));
              }}
            >
              {basePath}
            </Link>
            <span>
              {pathArr.map((item, index) =>
                item ? (
                  <>
                    &gt;{' '}
                    {index < pathArr.length - 2 ? <Link onClick={() => goToPath(index)}>{item}</Link> : item}
                  </>
                ) : null
              )}
            </span>
            <Clipboard
              text={`${basePath}${path}`}
              successMessage="复制成功"
              className="container-clipboard"
            />
          </>
        </div>
      </div>
      <Table
        rowSelection={{
          type: 'checkbox',
          ...rowSelection
        }}
        loading={loading}
        dataSource={fileListData.files || []}
        columns={columns}
        pagination={false}
        scroll={{x: 800}}
      />
      <div className={`${klass}-patination`}>
        <Pagination
          showSizeChanger
          pageSizeOptions={[10, 30, 50, 100, 200]}
          showTitle={false}
          pageSize={pagination.maxSize}
          onShowSizeChange={(current, size) => changePagination(null, size)}
        />
        <Button onClick={() => changePagination(PaginationType.MOVE_TO_FIRST)}>
          <LeftVertical
            theme="line"
            color="#6c6d70"
            size={16}
            strokeWidth={1}
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </Button>
        <Button
          disabled={pagination.marker === ''}
          onClick={() => changePagination(PaginationType.LEFT_SHIFT)}
        >
          <Left
            theme="line"
            color="#6c6d70"
            size={16}
            strokeWidth={1}
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </Button>
        <Button
          disabled={fileListData?.truncated === false}
          onClick={() => changePagination(PaginationType.RIGHT_SHIFT)}
        >
          <Right
            theme="line"
            color="#6c6d70"
            size={16}
            strokeWidth={1}
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </Button>
      </div>
    </div>
  );
};

export default forwardRef(VolumeFileInfoTable);
