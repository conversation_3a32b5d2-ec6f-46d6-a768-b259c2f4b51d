const {defineConfig} = require('@baidu/cba-cli');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const path = require('path');
const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');

// 自定义插件：在 entry 处理后打印入口文件地址
class EntryProcessedPlugin {
  constructor(options = {}) {
    this.options = options;
  }

  apply(compiler) {
    // 使用 entryOption hook，在 entry 被处理后同步触发
    compiler.hooks.entryOption.tap('EntryProcessedPlugin', (context, entry) => {
      console.log('\n=== EntryProcessedPlugin ===');
      console.log('Context:', context);
      console.log('Entry configuration:');

      if (typeof entry === 'string') {
        console.log('  Single entry:', entry);
      } else if (Array.isArray(entry)) {
        console.log('  Array entry:');
        entry.forEach((entryPath, index) => {
          console.log(`    [${index}]:`, entryPath);
        });
      } else if (typeof entry === 'object' && entry !== null) {
        console.log('  Object entry:');
        Object.entries(entry).forEach(([name, entryPath]) => {
          if (typeof entryPath === 'string') {
            console.log(`    ${name}:`, entryPath);
          } else if (Array.isArray(entryPath)) {
            console.log(`    ${name}:`);
            entryPath.forEach((path, index) => {
              console.log(`      [${index}]:`, path);
            });
          } else if (typeof entryPath === 'object') {
            console.log(`    ${name}:`, JSON.stringify(entryPath, null, 2));
          }
        });
      }
      console.log('=========================\n');
    });
  }
}

const templateId = '86089d33-aecc-424f-a241-784d4e94d08e'; // 公有云项目ID

module.exports = defineConfig({
  appName: 'edap/databuilder',
  presets: ['@baidu/cba-preset-console-react'],
  babelOptions: {
    plugins: ['@babel/plugin-proposal-optional-chaining'],
    resolveOptions: (babelConfig) => {
      babelConfig.presets = []; // 让其使用本地 .babelrc 配置
      return babelConfig;
    }
  },
  caching: true,
  rules: ['/api/databuilder/(.*)'],
  root: '.mockrc',
  // proxyTarget: 'https://qasandbox.bcetest.baidu.com',
  proxyTarget: 'https://console.bce.baidu.com/',
  i18n: {
    enabled: true,
    independent: false
  },
  templateId, // 功能清单ID
  flags: ['databuilder'], // 功能清单项, 要配置 DB 产品的功能清单
  webpack(config, merge) {
    // 这里merge无法生效，需要手动合并
    config.module.rules[0].oneOf.unshift({
      test: /tailwind\.css$/,
      use: [
        MiniCssExtractPlugin.loader,
        'css-loader',
        {
          loader: 'postcss-loader',
          options: {
            postcssOptions: {
              plugins: ['tailwindcss', 'postcss-preset-env']
            }
          }
        }
      ]
    });

    return merge(config, {
      module: {
        rules: [
          {
            // 此规则用于解决 webpack5 中的模块解析问题，针对 @baidu/xicon-react-bigdata 的解析问题
            test: /\.m?js/,
            resolve: {
              fullySpecified: false
            }
          }
        ]
      },
      resolve: {
        alias: {
          '@pages': path.resolve('./src/pages'),
          '@components': path.resolve('./src/components'),
          '@hooks': path.resolve('./src/hooks'),
          '@utils': path.resolve('./src/utils'),
          '@api': path.resolve('./src/api'),
          '@assets': path.resolve('./src/assets'),
          '@styles': path.resolve('./src/styles'),
          '@type': path.resolve('./src/type'),
          '@store': path.resolve('./src/store'),
          '@helpers': path.resolve('./src/helpers')
        }
      },
      plugins: [new MonacoWebpackPlugin(), new EntryProcessedPlugin()]
    });
  }
});
