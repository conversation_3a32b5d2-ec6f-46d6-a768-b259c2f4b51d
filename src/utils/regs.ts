/**
 * @file 正则校验规则
 * <AUTHOR>
 */

export const RULE = {
  // 中⽂、英⽂、数字、中划线、下划线，⻓度为1～50个字符
  specialName50: /^(?![-_])[\u4e00-\u9fa5a-zA-Z0-9-_]{0,50}$/,
  specialName50Text: '支持中文、英文、数字、中划线、下划线，长度为1～50个字符，不能以中划线、下划线开头',

  // 中⽂、英⽂、数字、中划线、下划线，⻓度为
  workflowName: /^[\u4e00-\u9fa5a-zA-Z0-9-_]{0,256}$/,
  workflowNameText: '支持中文、英文、数字、中划线、下划线',

  // 英文、数字、下划线，长度为1～64个字符
  specialName64: /^[a-zA-Z0-9_]{0,64}$/,
  specialName64Text: '支持英文、数字、下划线，长度为1～64个字符',
  // eslint-disable-next-line no-control-regex
  bos: /^([a-z0-9][a-z0-9-]{2,61}[a-z0-9])(\/((?!.*\/\/)(?!.*\/$)(?!.*\\$)(?!.*\.\.\/)[A-Za-z0-9-_=:\\/.]{1,254}))*$/,
  bosText: 'BOS路径格式不准确，请参考文档（https://cloud.baidu.com/doc/BOS/s/Fk4xtwbze）',
  hdfs: /^(?![\\/])(?!.*\/\/)([a-zA-Z0-9_-]{1,253})(\/[a-zA-Z0-9_-]{1,253})*$/,
  hdfsWithStart: /^hdfs:\/\/(?![\\/])(?!.*\/\/)([a-zA-Z0-9_-]{1,253})(\/[a-zA-Z0-9_-]{1,253})*$/,
  hdfsText: 'HDFS路径格式不准确'
};
