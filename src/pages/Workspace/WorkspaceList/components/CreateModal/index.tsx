/**
 * @file 工作空间 - 新建弹窗
 * <AUTHOR>
 */

import {FC, useCallback, useState} from 'react';
import {Form, Input, Link, Modal, toast} from 'acud';
import BosClient from '@api/bosClient';
import {
  createWorkspace,
  ICreateWorkspaceParams,
  IQueryWorkspaceListParams,
  checkWorkspaceNameDuplicate
} from '@api/workspace';
import BosSelect from '@components/BosSelect';
import {RULE} from '@utils/regs';
import flags from '@/flags';
import style from './index.module.less';

interface ICreateModalProps {
  isModalVisible: boolean;
  getWorkspaceList: (params?: IQueryWorkspaceListParams) => void;
  handleCloseModal: () => void;
}
const isPrivate = flags.DatabuilderPrivateSwitch;

const CreateModal: FC<ICreateModalProps> = ({isModalVisible, getWorkspaceList, handleCloseModal}) => {
  const [form] = Form.useForm();
  const [storageLocation, setStorageLocation] = useState<string>();

  const [loading, setLoading] = useState(false);

  // 关闭弹窗
  const onCloseModal = useCallback(() => {
    handleCloseModal();
    form.resetFields();
  }, [form, handleCloseModal]);

  // 提交表单
  const handleConfirm = useCallback(() => {
    if (loading) {
      return;
    }
    form
      .validateFields()
      .then((values) => {
        const {name, storageLocation, desc} = values;
        const storageLocationParam = isPrivate ? storageLocation : `bos://${storageLocation}`;
        const params: ICreateWorkspaceParams = {
          name,
          storageLocation: storageLocationParam,
          desc
        };
        setLoading(true);

        createWorkspace(params)
          .then((res) => {
            if (res?.success) {
              toast.success({
                message: '创建成功',
                duration: 5
              });

              onCloseModal();
              getWorkspaceList({
                pageNo: 1
              });
            }
          })
          .finally(() => {
            setLoading(false);
          });
      })
      .catch(() => {});
  }, [form, loading, onCloseModal, getWorkspaceList]);

  // 选择存储配置, bos sdk手动触发form填写
  const handleSelect = useCallback(
    (value) => {
      form.setFieldValue('storageLocation', value);
      form.validateFields(['storageLocation']);
      setStorageLocation(value);
    },
    [form]
  );

  // 跳转bos页面
  const jumpBos = () => {
    window.open('/bos', '_blank');
  };

  return (
    <Modal
      closable={true}
      title="创建工作空间"
      width={500}
      visible={isModalVisible}
      onOk={handleConfirm}
      onCancel={onCloseModal}
      okButtonProps={{
        loading
      }}
      destroyOnClose={true}
      className={style['workspace-modal']}
    >
      <Form labelAlign="left" layout="vertical" colon={false} labelWidth={80} form={form}>
        <Form.Item
          label="空间名称"
          name="name"
          validateFirst
          // extra={`${RULE.specialName50Text}，全局唯⼀`}
          validateDebounce={1000}
          rules={[
            {required: true, message: '请输入空间中文名称'},
            {
              validator: async (_, value) => {
                // 校验特殊字符和长度限制
                if (!RULE.specialName50.test(value)) {
                  return Promise.reject(new Error(`${RULE.specialName50Text}，全局唯⼀`));
                }
                // 校验空间名称是否重复
                const res = await checkWorkspaceNameDuplicate({name: value});
                if (res.success && res.result) {
                  return Promise.reject(new Error('该空间已存在，请重新输入'));
                }
                return Promise.resolve();
              }
            }
          ]}
        >
          <Input placeholder="请输入空间中文名称" allowClear limitLength={50} />
        </Form.Item>
        {isPrivate ? (
          <Form.Item
            label="存储配置"
            name="storageLocation"
            rules={[
              {required: true, message: '请输入存储配置'},
              {pattern: RULE.hdfs, message: RULE.hdfsText},
              {
                validator: (_, value) => {
                  // 创建空间时，hdfs路径最长限制 500
                  if (value.length > 500) {
                    return Promise.reject(new Error('最大长度不可超过500个字符'));
                  }
                  return Promise.resolve();
                }
              }
            ]}
            tooltip="云存储位置⽤于存储⼯作空间内的资产，例如：⽇志、作业、任务执⾏记录等"
            keepDisplayExtra
            extra={
              <div>
                <li>目录名称长度必须在1～254字节之间，文本框输入内容总长度不得超过500字节</li>
                <li>
                  支持填写英⽂、数字、中划线、下划线，/
                  用于分隔路径，可快速创建子目录，不能以/或\\字符开头，不能出现连续的/
                </li>
              </div>
            }
          >
            <Input value={storageLocation} placeholder="请输入目录名称，无需使用 / 结尾" />
          </Form.Item>
        ) : (
          <Form.Item
            label="存储配置"
            name="storageLocation"
            rules={[
              {required: true, message: '请选择存储配置'},
              {
                validator: (_, value) => {
                  // 创建空间时，bos路径最长限制 500
                  if (value?.length > 494 - 'bos://'.length) {
                    return Promise.reject(new Error('最大长度不可超过500个字符'));
                  }
                  return Promise.resolve();
                }
              }
            ]}
            tooltip="云存储位置⽤于存储⼯作空间内的资产，例如：⽇志、作业、任务执⾏记录等"
            keepDisplayExtra
            extra={
              <div>
                <span>如果没有bucket，请前往 </span>
                <Link onClick={jumpBos}>bos创建</Link>
              </div>
            }
          >
            <BosSelect handleSelect={handleSelect} value={storageLocation} />
          </Form.Item>
        )}

        <Form.Item label="描述" name="desc">
          <Input.TextArea
            placeholder="请输入空间描述"
            allowClear
            limitLength={200}
            style={{height: 80, maxHeight: 300, resize: 'vertical'}}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateModal;
